package com.jd.qf.ai.server.chat

import cn.hutool.core.date.DateUtil
import cn.hutool.core.util.RandomUtil
import com.alibaba.fastjson.JSON
import com.jd.qf.ai.biz.common.enums.AiChatRecordAcceptStatusEnum
import com.jd.qf.ai.biz.core.api.chat.ScrmAiChatService
import com.jd.qf.ai.biz.core.api.chat.bo.AiChatRecordPageQuery
import com.jd.qf.ai.biz.core.api.chat.bo.AsyncReplyCommand
import com.jd.qf.ai.biz.core.api.chat.bo.IntentQuery
import com.jd.qf.ai.biz.core.api.chat.bo.ReplyCommand
import com.jd.qf.ai.biz.core.api.chat.bo.StreamChatBo
import com.jd.qf.ai.biz.core.api.chat.bo.UpdateAcceptStatusQuery
import com.jd.qf.ai.biz.infrastructure.dao.mapper.AiChatRecordMapper
import com.jd.qf.ai.biz.infrastructure.rpc.chat.AgentChatRpcService
import com.jd.qf.ai.server.BaseTest
import com.jd.qf.ai.server.common.pojo.enums.IntentTypeEnum
import com.jd.qf.ai.server.common.pojo.resp.CommonStreamResponse
import com.jd.qf.ai.server.sdk.response.GeneralIntentResp
import com.jdt.open.exception.BizException
import groovy.util.logging.Slf4j
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.util.ReflectionTestUtils
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

import java.util.concurrent.TimeUnit

@Slf4j
class ScrmAiChatServiceImplTest extends BaseTest {

    @Autowired
    AiChatRecordMapper aiChatRecordMapper

    @Autowired
    ScrmAiChatService scrmAiChatService
    @SpringBean
    AgentChatRpcService agentChatRpcService=Mock()

    def "updateAcceptStatus-正常更新"() {
        given:
        def query = new UpdateAcceptStatusQuery()
        query.setRecordNo("REC20240601002")
        query.setAcceptStatus(AiChatRecordAcceptStatusEnum.ACCEPTED.getCode())
        when:
        scrmAiChatService.updateAcceptStatus(query)

        then:
        noExceptionThrown()
    }

    def "updateAcceptStatus-更新失败抛异常"() {
        given:
        def query = new UpdateAcceptStatusQuery()
        query.setRecordNo("R123")
        query.setAcceptStatus(AiChatRecordAcceptStatusEnum.ACCEPTED.getCode())
        aiChatRecordMapper.updateAcceptStatusByRecordNo(*_) >> 0

        when:
        scrmAiChatService.updateAcceptStatus(query)

        then:
        def e = thrown(BizException)
        e.message == "记录不存在"
    }

    def "pageQueryRecord-正常分页查询"() {
        given:
        def query = new AiChatRecordPageQuery()
        query.setProjectId(projectId)
        query.setCorpId(corpId)
        query.setCustId(custId)
        query.setSysUserId(sysUserId)
        query.locationType="BEFORE"
        query.setLocationMsgRecordId("REC20240601002")
        query.setLimit(10)
        query.setLocationMsgTime(new Date())

        when:
        def result = scrmAiChatService.pageQueryRecord(query)

        then:
        log.info("返回结果:{}", JSON.toJSONString(result))

        where:
        projectId | corpId    | custId    | sysUserId
        'PROJ001' | 'CORP001' | 'CUST001' | 1001
        'xxxxxxx' | 'CORP001' | 'CUST001' | 1001

    }

    def "意图识别"(){
        given:

        //mock配置
        Map<String, Map<String, Object>> map=new HashMap<>()
        Map<String, Object> configMap=new HashMap<>()
        configMap.put("agentId","jiuke")
        configMap.put("customerAddWechatLimitDays",30)
        configMap.put("messageContextTimeLimit",30)
        configMap.put("messageContextLimit",10)
        map.put("PRJ20240601",configMap)
        ReflectionTestUtils.setField(scrmAiChatService, "agentConfigMap", map)

        //mock AI基础服务
        agentChatRpcService.generalIntent(*_)>>{ Mono.just(new GeneralIntentResp(intentType: intentType)) }

        def query = new IntentQuery()
        query.setProjectId("PRJ20240601")
        query.setCorpId("CORP001")
        query.setCustId("CUST001")
        query.setCustName("客户001")
        query.setSysUserId(1)
        query.setContent("你好呀,为我推荐些商品吧"+ RandomUtil.randomString(5))
        query.setMsgRecordId("5")
        query.setMsgTime(msgTime)

        when:
        def result = scrmAiChatService.commonIntent(query).block()
        log.info("返回结果:{}",JSON.toJSONString(result))
        then:
        print(result)
        where:
        msgTime<<[DateUtil.parseDate("2025-05-09 17:53:19"),new Date()]
        intentType<<[IntentTypeEnum.REPLY.getCode(),IntentTypeEnum.NO_REPLY.getCode()]
    }

    def "流式AI回复"(){
        given:
        //mock配置
        Map<String, Map<String, Object>> map=new HashMap<>()
        Map<String, Object> configMap=new HashMap<>()
        configMap.put("agentId","jiuke")
        configMap.put("customerAddWechatLimitDays",30)
        configMap.put("messageContextTimeLimit",30)
        configMap.put("messageContextLimit",10)
        map.put("PRJ20240601",configMap)
        ReflectionTestUtils.setField(scrmAiChatService, "agentConfigMap", map)

        def command = new ReplyCommand()
        command.setProjectId("PRJ20240601")
        command.setMsgRecordId(recordId)

        agentChatRpcService.streamChat(*_)>>{ Flux.just(new CommonStreamResponse(answer: "推荐商品1",event: "message"),new CommonStreamResponse(answer: "推荐商品2",event: "message")) }

        when:
        def result = scrmAiChatService.streamReply(command).blockLast()
        log.info("返回结果:{}", JSON.toJSONString(result))
        then:
        print(result)
        where:
        recordId<<["1001","1002"]
    }

    def "异步AI回复"(){
        given:
        //mock配置
        Map<String, Map<String, Object>> map=new HashMap<>()
        Map<String, Object> configMap=new HashMap<>()
        configMap.put("agentId","jiuke")
        configMap.put("customerAddWechatLimitDays",30)
        configMap.put("messageContextTimeLimit",30)
        configMap.put("messageContextLimit",10)
        map.put("PRJ20240601",configMap)
        ReflectionTestUtils.setField(scrmAiChatService, "agentConfigMap", map)

        def command = new AsyncReplyCommand()
        command.setProjectId("PRJ20240601")
        command.setCorpId("CORP001")
        command.setCustId("CUST001")
        command.setCustName("客户001")
        command.setSysUserId(1)
        command.setContent("你好呀,为我推荐些商品吧"+ RandomUtil.randomString(5))
        command.setMsgRecordId("5")

        agentChatRpcService.generalIntent(*_)>>{ Mono.just(new GeneralIntentResp(intentType: IntentTypeEnum.REPLY.getCode())) }
        agentChatRpcService.streamChat(*_)>>{ Flux.just(new CommonStreamResponse(answer: "推荐商品1",event: "message"),new CommonStreamResponse(answer: "推荐商品2",event: "message")) }

        when:
        scrmAiChatService.asyncReply(command)
        TimeUnit.SECONDS.sleep(5)
        then:
        noExceptionThrown()
    }
}
