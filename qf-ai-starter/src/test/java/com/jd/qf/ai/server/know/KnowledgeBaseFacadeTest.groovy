package com.jd.qf.ai.server.chat

import com.jd.qf.ai.biz.api.knowledgebase.qa.KnowledgeBaseFacade
import com.jd.qf.ai.biz.api.knowledgebase.qa.dto.AddQAReq
import com.jd.qf.ai.server.BaseTest
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired


@Slf4j
class KnowledgeBaseFacadeTest extends BaseTest {

    @Autowired
    KnowledgeBaseFacade knowledgeBaseFacade;

    def "测试"(){

        given:
        AddQAReq req = new AddQAReq()
        when:
        knowledgeBaseFacade.addQA(req)
        then:
        notThrown(Exception)

    }
}
