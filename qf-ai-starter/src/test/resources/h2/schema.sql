-- AI聊天记录表（H2兼容版）
CREATE TABLE IF NOT EXISTS ai_chat_record (
  id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 自增主键
  record_no VARCHAR(64) NOT NULL, -- 记录编号
  corp_id VARCHAR(64) NOT NULL, -- 企业ID
  project_id VARCHAR(64), -- 项目ID
  project_name VARCHAR(128), -- 项目名称
  msg_record_id VARCHAR(64) NOT NULL, -- 消息ID
  msg_time TIMESTAMP NOT NULL, -- 消息时间
  sys_user_id INT NOT NULL, -- 员工ID
  sys_user_name VARCHAR(128) NOT NULL, -- 员工名称
  cust_id VARCHAR(64) NOT NULL, -- 客户ID
  cust_name VARCHAR(128) NOT NULL, -- 客户名称
  group_id VARCHAR(64) DEFAULT NULL, -- 群聊ID
  msg_text TEXT NOT NULL, -- 消息内容
  ai_answer TEXT, -- AI回复
  msg_context VARCHAR(2048), -- 消息上下文，map结构的json，带给大模型侧（H2无json类型，使用varchar存储）
  data_status VARCHAR(32) NOT NULL, -- 记录状态
  accept_status VARCHAR(32) NOT NULL, -- 采纳状态
  no_apply_reason VARCHAR(128), -- 不回复原因
  valid TINYINT DEFAULT 1 NOT NULL, -- 逻辑删除标志
  creator VARCHAR(32) NOT NULL, -- 创建人
  modifier VARCHAR(32), -- 最后更新人
  created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  modified_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  CONSTRAINT uniq_record_no UNIQUE (record_no)
);
TRUNCATE TABLE ai_chat_record;

CREATE TABLE IF NOT EXISTS oa_project (
                                          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                          corp_id VARCHAR(50) NOT NULL COMMENT '企业ID',
    project_code VARCHAR(50) NOT NULL COMMENT '项目编号',
    project_name VARCHAR(64) NOT NULL COMMENT '项目名称',
    contact VARCHAR(50) NOT NULL COMMENT '联系人',
    contact_phone VARCHAR(50) NOT NULL COMMENT '联系人手机号',
    status INT NOT NULL COMMENT '审核状态',
    fail_msg VARCHAR(200) COMMENT '驳回原因',
    master_id VARCHAR(50) COMMENT '机构ID',
    master_name VARCHAR(50) COMMENT '机构名称',
    audit_time TIMESTAMP COMMENT '审核时间',
    audit_user VARCHAR(50) COMMENT '审核人',
    version INT NOT NULL COMMENT '版本号',
    is_valid BOOLEAN NOT NULL COMMENT '是否有效标识（false：无效、true：有效）',
    creator VARCHAR(50) NOT NULL COMMENT '创建人',
    modifier VARCHAR(50) COMMENT '修改人',
    created_date TIMESTAMP NOT NULL COMMENT '创建时间',
    modified_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    customer_id VARCHAR(32) NOT NULL COMMENT '客户ID',
    customer_name VARCHAR(255) COMMENT '客户名称',
    depart_id VARCHAR(64) COMMENT '项目对应的部门ID',
    start_date TIMESTAMP COMMENT '项目开始时间',
    end_date TIMESTAMP COMMENT '项目结束时间'
    );
TRUNCATE TABLE oa_project;


CREATE TABLE IF NOT EXISTS sys_user (
                                        id INT AUTO_INCREMENT COMMENT '用户ID',
                                        corp_id VARCHAR(64) DEFAULT NULL COMMENT '企业ID',
    login_mobile VARCHAR(32) DEFAULT NULL COMMENT '登录手机号',
    login_pin VARCHAR(64) DEFAULT NULL COMMENT '员工登录PIN码',
    user_name VARCHAR(64) DEFAULT NULL COMMENT '用户名称',
    sex VARCHAR(10) DEFAULT NULL COMMENT '性别',
    login_pwd VARCHAR(100) DEFAULT '$2a$10$K5XWcjTTfGtUUlpXEWfNieltL.Gm4wjmIzhzzF5GasWdfJlxMB93O' COMMENT '登录密码',
    user_type VARCHAR(10) DEFAULT '0' COMMENT '用户类型',
    is_mobile_valid TINYINT DEFAULT 0 COMMENT '手机号是否验证',
    head_img_url VARCHAR(300) DEFAULT NULL COMMENT '头像URL',
    is_agree TINYINT DEFAULT 0 COMMENT '是否同意协议',
    agree_time DATETIME DEFAULT NULL COMMENT '同意协议时间',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_login_time TIMESTAMP DEFAULT NULL COMMENT '最后登录时间',
    pwd_update_time DATETIME DEFAULT NULL COMMENT '密码更新时间',
    upper_user_id VARCHAR(255) DEFAULT NULL COMMENT '上级用户ID',
    depart_id INT DEFAULT NULL COMMENT '部门ID',
    status TINYINT DEFAULT 0 COMMENT '用户状态',
    wx_open_id VARCHAR(30) DEFAULT NULL COMMENT '微信开放ID',
    mobile_device_id VARCHAR(30) DEFAULT NULL COMMENT '移动设备ID',
    wx_open_id_ai VARCHAR(30) DEFAULT NULL COMMENT '微信AI开放ID',
    wx_open_id_op VARCHAR(30) DEFAULT NULL COMMENT '微信运营开放ID',
    ww_user_id VARCHAR(64) DEFAULT NULL COMMENT '企业微信用户ID',
    ww_open_id VARCHAR(32) DEFAULT NULL COMMENT '企业微信开放ID',
    login_control VARCHAR(100) DEFAULT NULL COMMENT '登录控制',
    rank_type VARCHAR(20) DEFAULT 'STAFF' COMMENT '职级类型',
    ww_contact_status TINYINT DEFAULT 0 COMMENT '企业微信联系人状态',
    ww_head_img_url VARCHAR(300) DEFAULT NULL COMMENT '企业微信头像URL',
    ww_user_name VARCHAR(50) DEFAULT NULL COMMENT '企业微信用户名',
    department VARCHAR(255) DEFAULT NULL COMMENT '部门信息',
    is_leader_in_dept VARCHAR(255) DEFAULT NULL COMMENT '是否部门负责人',
    last_contact_time DATETIME DEFAULT NULL COMMENT '最后联系时间',
    update_time TIMESTAMP DEFAULT NULL COMMENT '更新时间',
    is_activate INT DEFAULT 0 COMMENT '是否激活',
    source VARCHAR(64) DEFAULT NULL COMMENT '来源',
    red_jurisdiction INT DEFAULT NULL COMMENT '红包权限',
    wx_sys_user_id VARCHAR(64) DEFAULT NULL COMMENT '微信系统用户ID',
    open_user_id VARCHAR(50) DEFAULT NULL COMMENT '开放用户ID',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    project_id VARCHAR(32) DEFAULT NULL COMMENT '项目ID',
    project_name VARCHAR(255) DEFAULT NULL COMMENT '项目名称',
    customer_id VARCHAR(32) DEFAULT NULL COMMENT '客户ID',
    customer_name VARCHAR(255) DEFAULT NULL COMMENT '客户名称',
    PRIMARY KEY (id));

TRUNCATE TABLE sys_user;

CREATE TABLE `cust` (
`id` varchar(56)  NOT NULL COMMENT '临时备注',
`data_type` varchar(30)  DEFAULT 'CLUE' COMMENT '数据类型 CLUE-线索池 CUST-客户池',
`name` varchar(50)  DEFAULT NULL COMMENT '姓名，为空时，显示客户+ID',
`sex` varchar(30)  DEFAULT NULL COMMENT '性别：男、女',
`birthday` date DEFAULT NULL COMMENT '生日',
`age_group` varchar(100)  DEFAULT NULL COMMENT '年龄段：18岁及以下、19-25岁、26-35岁、36-45岁、46-55岁、56-65岁、65岁及以上',
`marital` varchar(30)  DEFAULT NULL COMMENT '婚姻状况：未知、 已婚、 未婚、 离异',
`vocation` varchar(30)  DEFAULT NULL COMMENT '职业',
`mobile_id` varchar(32)  DEFAULT NULL COMMENT '手机号脱敏显示',
`mobile` varchar(210)  DEFAULT NULL COMMENT '手机号',
`telephone_id` varchar(30)  DEFAULT NULL COMMENT '联系电话脱敏显示',
`telephone` varchar(210)  DEFAULT NULL COMMENT '联系电话',
`qq` varchar(30)  DEFAULT NULL COMMENT 'qq',
`email` varchar(255)  DEFAULT NULL COMMENT '邮箱',
`edu_level` varchar(30)  DEFAULT NULL COMMENT '文化水平 ：初中及以下，中专/高中，专科，本科及以上',
`province` varchar(30)  DEFAULT NULL COMMENT '省',
`city` varchar(30)  DEFAULT NULL COMMENT '城市',
`county` varchar(30)  DEFAULT NULL COMMENT '市/县',
`company` varchar(50)  DEFAULT NULL COMMENT '所在工作公司',
`home_address` varchar(200)  DEFAULT NULL COMMENT '家庭住址',
`company_address` varchar(200)  DEFAULT NULL COMMENT '公司住址',
`child_age_group` varchar(50)  DEFAULT NULL COMMENT '小孩年龄:0-6岁，7-12岁，13-16岁',
`mobile_brand` varchar(50)  DEFAULT NULL COMMENT '手机品牌',
`source` varchar(50)  DEFAULT NULL COMMENT '来源类别：ARC、门店、企业、营销',
`source_ch` varchar(50)  DEFAULT NULL COMMENT '数据来源渠道：见客户来源枚举对应关系',
`wx_open_id` varchar(50)  DEFAULT NULL COMMENT '小程序微信id',
`assign_time` datetime DEFAULT NULL COMMENT '客户最近分配时间',
`assign_user_id` int(11) DEFAULT NULL COMMENT '分配负责人id',
`project_id` varchar(32) DEFAULT NULL COMMENT '项目ID',
`assign_user_name` varchar(50)  DEFAULT NULL COMMENT '分配负责人姓名',
`status` tinyint(1) DEFAULT '1' COMMENT '状态 状态 1 正常 9 伪删除',
`last_reach_time` datetime DEFAULT NULL COMMENT '最后触达时间',
`corp_id` varchar(64)  DEFAULT NULL COMMENT '临时备注',
`avatar` varchar(200)  DEFAULT NULL COMMENT '头像',
`ww_user_id` varchar(64)  DEFAULT NULL COMMENT '用户关联ID',
`external_userid` varchar(32)  DEFAULT NULL COMMENT '外部联系人ID',
`corp_name` varchar(50)  DEFAULT NULL COMMENT '企微企业名',
`corp_full_name` varchar(200)  DEFAULT NULL COMMENT '企微企业全名',
`head_img_url` varchar(50)  DEFAULT '' COMMENT '企微头像',
`channel_id` int(11) DEFAULT NULL COMMENT '渠道Id',
`state` varchar(50)  DEFAULT NULL COMMENT '渠道',
`add_way` int(11) DEFAULT NULL COMMENT '添加客户的来源',
`oper_userid` varchar(64)  DEFAULT NULL COMMENT '临时备注',
`remark` varchar(50)  DEFAULT NULL COMMENT '该负责人对此外部联系人的备注',
`remark_corp_name` varchar(50)  DEFAULT NULL COMMENT '该成员对此客户备注的企业名称',
`remark_mobiles` varchar(255)  DEFAULT NULL COMMENT '该成员对此客户备注的手机号码',
`description` varchar(255)  DEFAULT NULL COMMENT '该成员对此外部联系人的描述\r\n',
`is_loss` tinyint(4) DEFAULT '0' COMMENT '流失状态 1是 0否',
`lifecycle` varchar(30)  DEFAULT NULL COMMENT '默认生命周期配置:新客户;初步沟通;意向客户;付款用户;无意向用户',
`ww_type` tinyint(4) DEFAULT NULL COMMENT '外部联系人的类型，1表示该外部联系人是微信用户，2表示该外部联系人是企业微信用户',
`position` varchar(32)  DEFAULT NULL COMMENT '职务信息',
`update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`delete_time` timestamp NULL DEFAULT NULL COMMENT '删除时间-即使员工重新加了用户，这里也不要清空，有统计依赖了这里',
`create_user_id` int(11) DEFAULT NULL COMMENT '创建人id',
`create_user_name` varchar(50)  DEFAULT NULL COMMENT '创建者姓名',
`wx_cust_user_id` varchar(64)  DEFAULT NULL COMMENT '企微聊天相关，客户Id',
`drain_time` timestamp NULL DEFAULT NULL COMMENT '客户流失时间-即使用户重新加了员工，这里也不要清空，有统计依赖了这里',
`one_cust_one_code_id` bigint(20) DEFAULT NULL COMMENT '一客一码id',
`point` bigint(20) NOT NULL DEFAULT '0' COMMENT '当前积分',
`freeze_point` bigint(20) NOT NULL DEFAULT '0' COMMENT '冻结积分',
`used_point` bigint(20) NOT NULL DEFAULT '0' COMMENT '消耗的积分',
`expired_point` bigint(20) NOT NULL DEFAULT '0' COMMENT '过期积分',
`all_point` bigint(20) NOT NULL DEFAULT '0' COMMENT '总积分',
`avatar_finger_print` varchar(64)  DEFAULT NULL COMMENT '头像指纹',
`created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`modified_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`jd_pin` varchar(255)  DEFAULT NULL COMMENT '京东PIN',
`unionid` varchar(255)  DEFAULT NULL COMMENT '开发者唯一标识',
`member_id` varchar(32)  DEFAULT NULL COMMENT '会员ID',
`business_pin` varchar(255)  DEFAULT NULL COMMENT '商绑pin',
PRIMARY KEY (`id`)) ;
TRUNCATE TABLE cust;

CREATE TABLE IF NOT EXISTS `cs_chat_record_new` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '消息ID',
    `cs_id` int(11) DEFAULT NULL COMMENT '客服id cs_info id',
    `robot_id` int(11) DEFAULT NULL COMMENT '员工id sys_user id',
    `wx_cust_id` varchar(50) DEFAULT NULL COMMENT '第三方微信客户id cust wx_cust_user_id',
    `wx_group_id` varchar(50) DEFAULT NULL COMMENT '第三方微信群id sys_group room_chat_id',
    `wx_user_id` varchar(50) DEFAULT NULL COMMENT '第三方员工id sys_user wx_sys_user_id',
    `cust_id` varchar(64) DEFAULT NULL COMMENT '客户id cust id',
    `corp_id` varchar(64) DEFAULT NULL COMMENT '企业id',
    `msg_type` varchar(20) NOT NULL DEFAULT 'text' COMMENT '具体的消息类型',
    `msg_json` text NOT NULL COMMENT '消息json',
    `chat_type` varchar(10) DEFAULT NULL COMMENT 'SEND-发送 RECEIVE-接收',
    `fans_type` varchar(10) DEFAULT NULL COMMENT '粉丝类型:群消息-group fans-好友',
    `send_time` datetime DEFAULT NULL COMMENT '发送时间',
    `receive_time` datetime DEFAULT NULL COMMENT '接受时间',
    `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `gmt_update` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status` int(11) DEFAULT '1' COMMENT '状态',
    `chat_id` varchar(50) DEFAULT NULL COMMENT '会话id',
    `msg_id` varchar(512) DEFAULT NULL COMMENT '消息id',
    `msg_send_status` int(11) DEFAULT NULL COMMENT '消息状态 1：发送中，2：发送成功，3：发送失败',
    `sha` varchar(255) DEFAULT NULL COMMENT '临时备注',
    `msg_unique_identifier` varchar(255) DEFAULT NULL COMMENT '临时备注',
    `sensitive_words_flag` int(11) DEFAULT '0' COMMENT '敏感词处理标记 0：未处理 ，1：已处理',
    `failed_msg` varchar(200) NOT NULL DEFAULT '' COMMENT '发送失败消息提示语',
    `is_analysed_sentiment` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '情感分析状态,0-未分析,1-等待结果返回,2-积极情绪,3-无情绪,4-消极情绪',
    `is_top` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否标记(即置顶，用于情感分析消息列表) 1-是 0-否',
    `stick_on_top_time` datetime DEFAULT NULL COMMENT '最新标记时间',
    `un_read` tinyint(3) UNSIGNED NOT NULL DEFAULT '1' COMMENT '是否未读 1-未读 0-已读',
    `is_delete` tinyint(3) UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否删除 1-已删除 0-未删除',
    `recall` tinyint(4) DEFAULT '0' COMMENT '是否撤回 0:否 1:是',
    `recall_id` varchar(512) DEFAULT NULL COMMENT '撤回消息用id',
    `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modified_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`)) COMMENT '客服聊天记录表';
TRUNCATE TABLE cs_chat_record_new;

CREATE TABLE IF NOT EXISTS `ai_know_group` (
  `id` bigint(20) AUTO_INCREMENT NOT NULL COMMENT '自增主键',
  `project_id` varchar(64) NOT NULL COMMENT '项目ID',
  `project_name` varchar(128) NOT NULL COMMENT '项目名称',
  `group_no` varchar(64) NOT NULL COMMENT '分组编码',
  `group_name` varchar(64) NOT NULL COMMENT '分组名称',
  `group_type` varchar(32) NOT NULL COMMENT '分组类型：DEFAULT-默认分组，NORMAL-普通分组',
  `valid` tinyint(1) DEFAULT 1 NOT NULL COMMENT '逻辑删除标志,1:正常记录,0:已逻辑删除',
  `creator` varchar(32) NOT NULL COMMENT '创建人',
  `modifier` varchar(32) COMMENT '最后更新人',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modified_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_group_no` (`group_no`),
  KEY `index_project_id` (`project_id`)
) COMMENT '知识库分组表';
TRUNCATE TABLE ai_know_group;
