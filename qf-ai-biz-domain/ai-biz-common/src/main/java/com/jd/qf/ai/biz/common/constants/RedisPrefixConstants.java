package com.jd.qf.ai.biz.common.constants;

/**
 * redis前缀
 * <AUTHOR>
 * @description
 * @date 2025/4/10
 */
public class RedisPrefixConstants {

    /**
     * 有效回复限制
     */
    public static final String MESSAGE_REPLY_LIMIT = "MESSAGE_REPLY_LIMIT:";

    /**
     * 回复去重限制
     */
    public static final String MESSAGE_REPLY_DEDUPE = "MESSAGE_REPLY_DEDUPE:";

    /**
     * 客户维度的会话基本信息前缀
     */
    public static final String CHAT_SESSION_INFO = "CUST_CONVERSATION_BASE_INFO:";

    /**
     * 插入AI回复记录锁
     */
    public static final String INSERT_AI_RECORD = "INSERT_AI_RECORD:";

    /**
     * 异步请求AI回复锁
     */
    public static final String ASYNC_REPLY_LOCK = "ASYNC_REPLY_LOCK:";

    /**
     * 前端请求AI回复标
     */
    public static final String FRONTEND_REQUEST = "FRONTEND_REQUEST:";

    /**
     * AI回复未读数
     */
    public static final String AI_UNREAD = "AI_UNREAD:";

    /**
     * 客户给员工的群聊消息去重
     */
    public static final String GROUP_MESSAGE_DEDUPE = "GROUP_MESSAGE_DEDUPE:";
}
