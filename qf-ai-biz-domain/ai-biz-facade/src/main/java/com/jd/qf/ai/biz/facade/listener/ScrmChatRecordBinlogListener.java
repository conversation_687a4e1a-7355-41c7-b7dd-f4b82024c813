package com.jd.qf.ai.biz.facade.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jd.qf.ai.biz.common.dto.CsChatRecordModel;
import com.jd.qf.ai.biz.common.enums.ChatTypeEnum;
import com.jd.qf.ai.biz.common.enums.FansTypeEnum;
import com.jd.qf.ai.biz.common.enums.WxMsgTypeEnum;
import com.jd.qf.ai.biz.core.api.chat.ScrmMqAiChatService;
import com.jd.qf.ai.biz.facade.listener.dto.BinlogMessage;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.SysUserMapper;
import com.jd.qf.ai.biz.infrastructure.dao.po.SysUserPo;
import com.jdt.open.capability.client.redis.OpenRedisClient;
import com.jdt.open.exception.BizException;
import com.jdt.open.exception.ResponseCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.jd.qf.ai.biz.common.constants.RedisPrefixConstants.GROUP_MESSAGE_DEDUPE;

/**
 * cs_chat_record_new表binlog监听器
 * 只监听用户发的消息
 * <AUTHOR>
 * @date 2024/11/29 14:45
 */
@Slf4j
@Service
public class ScrmChatRecordBinlogListener implements MessageListener {

    @Autowired
    private ScrmMqAiChatService scrmMqAiChatService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private OpenRedisClient openRedisClient;

    @Override
    public void onMessage(List<Message> list) {

        for (Message message : list) {
            solveMessage(message);
        }
    }

    private void solveMessage(Message message) {
        try {
            String text = message.getText();
            if (StringUtils.isBlank(message.getText())) {
                return;
            }
            log.debug("cs_chat_record_new表binlog监听器收到消息:{}", text);
            BinlogMessage messageDto = JSON.parseObject(text, BinlogMessage.class);
            if (Boolean.FALSE.equals(messageDto.isValidMessage())) {
                log.error("binlog不合法,不进行处理");
                return;
            }
            CsChatRecordModel csChatRecordModel = messageDto.getCsChatRecordModel();
            if (!filterMsg(csChatRecordModel)) {
                log.debug("不关注的群控消息");
                return;
            }
            scrmMqAiChatService.processBinLog(csChatRecordModel);
        } catch (Exception e) {
            log.error("cs_chat_record_new表binlog监听器异常,抛出异常后开始重试", e);
            throw new BizException(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    private boolean filterMsg(CsChatRecordModel csChatRecordModel) {
        if (csChatRecordModel == null) {
            log.error("消息体为空");
            return false;
        }
        if (!StringUtils.equals(ChatTypeEnum.RECEIVE.getKey(), csChatRecordModel.getChatType())) {
            log.debug("不是接收消息");
            return false;
        }
        if (StringUtils.isBlank(csChatRecordModel.getMsgType())) {
            log.debug("消息类型为空");
            return false;
        }
        if (!StringUtils.equals(WxMsgTypeEnum.TEXT.getWxMsgType(), csChatRecordModel.getMsgType())
                && !StringUtils.equals(WxMsgTypeEnum.MIXTEXT.getWxMsgType(), csChatRecordModel.getMsgType())) {
            log.debug("不是文本消息");
            return false;
        }

        //过滤掉链接的文本
        String msgJson = csChatRecordModel.getMsgJson();
        if (StringUtils.contains(msgJson, "http")
                || StringUtils.contains(msgJson, ".com")
                || StringUtils.contains(msgJson, ".cn")) {
            log.debug("消息包含链接,不进行处理");
            return false;
        }

        //过滤一种特殊的场景,群聊场景下,员工也会收到一条消息,这条消息的发送者是员工自己,这种消息不需要处理
        if (FansTypeEnum.GROUP.getCode().equals(csChatRecordModel.getFansType())){

            //过滤掉群场景下,客户只发了一条消息,但会有多个群里员工收到receive消息的情况
            String groupId = csChatRecordModel.getGroupId();
            String wxCustId = csChatRecordModel.getWxCustId();
            String receiveTime = DateUtil.formatDateTime(csChatRecordModel.getReceiveTime());
            String cacheKey=GROUP_MESSAGE_DEDUPE+groupId+wxCustId+receiveTime;
            if (openRedisClient.get(cacheKey) != null) {
                log.debug("群聊场景下,客户只发了一条消息,但会有多个群里员工收到receive消息的情况,不进行处理");
                return false;
            } else {
                openRedisClient.setNx(cacheKey, cacheKey, 2000);
            }

            List<SysUserPo> sysUserPos = sysUserMapper.queryByWxSysUserId(wxCustId);
            if (CollectionUtil.isNotEmpty(sysUserPos)){
                log.debug("群聊场景下,员工也会收到一条消息,这条消息的发送者是员工自己,这种消息不需要处理");
                return false;
            }
        }
        return true;
    }
}
