package com.jd.qf.ai.biz.core.service.chat.core;

import com.jd.qf.ai.biz.common.enums.FansTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * SCRM核心AI服务工厂类
 * 根据fansType返回对应的处理器实例
 * <AUTHOR>
 * @description
 * @date 2025/5/29
 */
@Slf4j
@Component
public class ScrmCoreAiServiceFactory {

    @Autowired
    private FansCoreAiHandler fansCoreAiHandler;

    @Autowired
    private GroupCoreAiHandler groupCoreAiHandler;

    /**
     * 根据fansType获取对应的AI服务处理器
     * @param fansType 粉丝类型：FANS-私聊，GROUP-群聊
     * @return 对应的AI服务处理器
     */
    public ScrmCoreAiService getService(String fansType) {
        if (FansTypeEnum.FANS.getCode().equals(fansType)) {
            log.debug("获取私聊场景AI处理器");
            return fansCoreAiHandler;
        } else if (FansTypeEnum.GROUP.getCode().equals(fansType)) {
            log.debug("获取群聊场景AI处理器");
            return groupCoreAiHandler;
        } else {
            log.error("未知的fansType类型：{}，默认使用私聊处理器", fansType);
            return fansCoreAiHandler;
        }
    }
}
