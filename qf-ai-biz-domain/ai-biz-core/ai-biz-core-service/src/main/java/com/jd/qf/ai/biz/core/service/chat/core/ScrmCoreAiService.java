package com.jd.qf.ai.biz.core.service.chat.core;

import com.jd.qf.ai.biz.core.api.chat.bo.IntentBo;
import com.jd.qf.ai.biz.core.api.chat.bo.IntentQuery;
import com.jd.qf.ai.biz.core.api.chat.bo.ReplyCommand;
import com.jd.qf.ai.biz.core.api.chat.bo.StreamChatBo;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * SCRM AI 核心服务
 * <AUTHOR>
 * @description
 * @date 2025/5/29
 */
public interface ScrmCoreAiService {

    /**
     * 通用意图识别
     */
    Mono<IntentBo> commonIntent(IntentQuery query);
}
