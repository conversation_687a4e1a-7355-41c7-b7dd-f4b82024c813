package com.jd.qf.ai.biz.core.service.chat.core;

import cn.hutool.core.date.DateUtil;
import com.jd.qf.ai.biz.common.constants.RedisPrefixConstants;
import com.jd.qf.ai.biz.core.api.chat.bo.ConversationBaseInfo;
import com.jd.qf.ai.biz.core.api.chat.bo.IntentQuery;
import com.jd.qf.ai.biz.infrastructure.dao.po.CustPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 群AI处理器
 * <AUTHOR>
 * @description
 * @date 2025/5/29
 */
@Service
@Slf4j
public class GroupCoreAiHandler extends AbstractScrmCoreAiService{

    @Override
    protected Map<String, Object> buildIntentParams(IntentQuery query, ConversationBaseInfo baseInfo) {
        Map<String, Object> params = new HashMap<>();
        params.put("project_id", query.getProjectId());
        params.put("add_wx_time", DateUtil.formatDateTime(baseInfo.getCustCreateTime()));

        // 群聊场景：不需要查询历史会话状态，pre_state为null
        params.put("pre_state", null);
        return params;
    }

    @Override
    protected String getBaseInfoKey(IntentQuery query) {
        return RedisPrefixConstants.CHAT_SESSION_INFO + query.getGroupId() + "_" + query.getWxCustId();
    }

    @Override
    protected CustPo getCustPo(IntentQuery query) {
        return custMapper.queryByGroupMember(query.getGroupId(), query.getWxCustId());
    }
}
