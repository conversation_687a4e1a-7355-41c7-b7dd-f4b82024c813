package com.jd.qf.ai.biz.core.service.chat.core;

import cn.hutool.core.date.DateUtil;
import com.jd.qf.ai.biz.common.constants.RedisPrefixConstants;
import com.jd.qf.ai.biz.core.api.chat.bo.ConversationBaseInfo;
import com.jd.qf.ai.biz.core.api.chat.bo.IntentQuery;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.CustPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 私聊场景下的AI处理器
 * <AUTHOR>
 * @description
 * @date 2025/5/29
 */
@Slf4j
@Service
public class FansCoreAiHandler extends AbstractScrmCoreAiService{

    @Override
    protected Map<String, Object> buildIntentParams(IntentQuery query, ConversationBaseInfo baseInfo) {
        Map<String, Object> params = new HashMap<>();
        params.put("project_id", query.getProjectId());
        params.put("add_wx_time", DateUtil.formatDateTime(baseInfo.getCustCreateTime()));

        // 私聊场景：查询客户的历史会话状态
        AiChatRecordPo lastPo = aiChatRecordMapper.selectLastAiRecord(query.getCustId());
        params.put("pre_state", lastPo == null ? null : lastPo.getSessionState());
        return params;
    }

    @Override
    protected String getBaseInfoKey(IntentQuery query) {
        return RedisPrefixConstants.CHAT_SESSION_INFO + query.getCustId();
    }

    @Override
    protected CustPo getCustPo(IntentQuery query) {
        return custMapper.queryByCustId(query.getCustId());
    }
}
